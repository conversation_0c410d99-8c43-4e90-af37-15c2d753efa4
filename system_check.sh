#!/bin/bash
echo "=== Warrior Clash 3D System Check ==="

# Check Node.js
if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js: Not installed"
    echo "   Please install from: https://nodejs.org/"
fi

# Check pnpm
if command -v pnpm &> /dev/null; then
    echo "✅ pnpm: $(pnpm --version)"
else
    echo "❌ pnpm: Not installed"
    echo "   Install with: npm install -g pnpm"
fi

# Check system architecture
ARCH=$(uname -m)
echo "✅ Architecture: $ARCH"
if [[ "$ARCH" == "arm64" ]]; then
    echo "   ✅ Apple Silicon detected - Optimal performance expected"
else
    echo "   ⚠️  Non-Apple Silicon detected - Performance may vary"
fi

# Check available memory (macOS specific)
if [[ "$OSTYPE" == "darwin"* ]]; then
    MEMORY=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
    echo "✅ Memory: ${MEMORY}GB"
    if (( $(echo "$MEMORY >= 8" | bc -l) )); then
        echo "   ✅ Sufficient memory for game"
    else
        echo "   ⚠️  Low memory - May affect performance"
    fi
else
    echo "✅ Memory: $(free -h | awk '/^Mem:/ {print $2}')"
fi

# Check available disk space
DISK_SPACE=$(df -h . | tail -1 | awk '{print $4}')
echo "✅ Disk Space: $DISK_SPACE available"

# Check if in game directory
if [[ -f "package.json" && -f "vite.config.js" ]]; then
    echo "✅ Game directory: Detected"
    
    # Check if dependencies are installed
    if [[ -d "node_modules" ]]; then
        echo "✅ Dependencies: Installed"
    else
        echo "❌ Dependencies: Not installed"
        echo "   Run: pnpm install"
    fi
else
    echo "⚠️  Game directory: Not detected"
    echo "   Make sure you're in the warrior-clash-3d directory"
fi

# Check browser availability
if command -v open &> /dev/null; then
    echo "✅ Browser: Available (macOS)"
elif command -v xdg-open &> /dev/null; then
    echo "✅ Browser: Available (Linux)"
else
    echo "⚠️  Browser: Manual opening required"
fi

echo ""
echo "=== System Check Complete ==="
echo ""

# Provide next steps
if command -v node &> /dev/null && command -v pnpm &> /dev/null; then
    echo "🎮 Ready to play! Run the following commands:"
    echo "   pnpm install    # Install dependencies (if not done)"
    echo "   pnpm run dev    # Start the game"
    echo ""
    echo "   Then open: http://localhost:5173/"
else
    echo "❌ Please install missing requirements before running the game"
fi

