# Warrior Clash 3D - Epic Battle Arena

A 3D combat game featuring <PERSON>urai, Mongol, and Viking warriors with Middle Eastern music, optimized for Mac M1 Air systems.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Installation Guide](#installation-guide)
3. [Game Features](#game-features)
4. [Character Classes](#character-classes)
5. [Game Mechanics](#game-mechanics)
6. [Controls](#controls)
7. [Technical Specifications](#technical-specifications)
8. [Troubleshooting](#troubleshooting)
9. [Development](#development)

## System Requirements

### Minimum Requirements
- **Operating System**: macOS 11.0 (Big Sur) or later
- **Processor**: Apple M1 chip or later
- **Memory**: 8GB RAM
- **Storage**: 6GB available space
- **Graphics**: Integrated Apple M1 GPU
- **Audio**: Built-in speakers or headphones

### Recommended Requirements
- **Operating System**: macOS 12.0 (Monterey) or later
- **Processor**: Apple M1 Pro/Max or M2
- **Memory**: 16GB RAM
- **Storage**: 10GB available space
- **Graphics**: Apple M1 Pro/Max GPU or M2 GPU
- **Audio**: High-quality headphones for optimal music experience

### Compatibility Check
Your Mac M1 Air with 8GB RAM and 256GB SSD meets the minimum requirements for this game. The game is specifically optimized for Apple Silicon architecture and will run smoothly on your system.

## Installation Guide

### Prerequisites

Before installing the game, ensure you have the following software installed:

#### 1. Node.js (Required)
```bash
# Check if Node.js is installed
node --version

# If not installed, download from: https://nodejs.org/
# Recommended version: 18.x or later
```

#### 2. pnpm Package Manager (Required)
```bash
# Check if pnpm is installed
pnpm --version

# If not installed, install via npm:
npm install -g pnpm
```

#### 3. Git (Optional, for development)
```bash
# Check if Git is installed
git --version

# If not installed, download from: https://git-scm.com/
```

### Installation Steps

1. **Download the Game**
   ```bash
   # Navigate to your desired installation directory
   cd ~/Games
   
   # Extract the game files (if downloaded as archive)
   # Or clone from repository:
   git clone [repository-url] warrior-clash-3d
   ```

2. **Install Dependencies**
   ```bash
   cd warrior-clash-3d
   pnpm install
   ```

3. **Verify Installation**
   ```bash
   # Check if all dependencies are installed correctly
   pnpm list
   ```

4. **Run the Game**
   ```bash
   # Start the development server
   pnpm run dev
   
   # The game will be available at: http://localhost:5173/
   ```

### Verification Script

Create a verification script to ensure your system is ready:

```bash
#!/bin/bash
echo "=== Warrior Clash 3D System Check ==="

# Check Node.js
if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js: Not installed"
fi

# Check pnpm
if command -v pnpm &> /dev/null; then
    echo "✅ pnpm: $(pnpm --version)"
else
    echo "❌ pnpm: Not installed"
fi

# Check system architecture
echo "✅ Architecture: $(uname -m)"

# Check available memory
echo "✅ Memory: $(sysctl hw.memsize | awk '{print $2/1024/1024/1024 " GB"}')"

# Check available disk space
echo "✅ Disk Space: $(df -h . | tail -1 | awk '{print $4}') available"

echo "=== System Check Complete ==="
```

Save this as `system_check.sh` and run with `bash system_check.sh`.

## Game Features

### Core Features
- **3D Battle Arena**: Immersive 3D environment with realistic lighting and shadows
- **Three Warrior Classes**: Choose from Samurai, Mongol, or Viking warriors
- **Turn-Based Combat**: Strategic combat system with attack and defense options
- **Middle Eastern Music**: Atmospheric background music enhancing the gaming experience
- **Real-Time Health Tracking**: Visual health bars and damage indicators
- **Interactive 3D Models**: Rotating character models with distinct visual styles

### Visual Features
- **Responsive Design**: Optimized for both desktop and mobile viewing
- **Dynamic Lighting**: Real-time lighting effects in the 3D arena
- **Particle Effects**: Combat effects and visual feedback
- **Smooth Animations**: Fluid character movements and transitions

### Audio Features
- **Background Music**: High-quality Middle Eastern music track
- **Music Controls**: Play/pause functionality for background music
- **Audio Optimization**: Optimized for Mac M1 audio processing

## Character Classes

### Samurai
The Samurai represents the pinnacle of disciplined warfare, embodying the bushido code of honor and precision. These warriors excel in balanced combat with strong defensive capabilities.

**Statistics:**
- Health: 100
- Power: 25
- Defense: 20
- Speed: Fast

**Special Abilities:**
- **Counter Attack**: A devastating counter-attack that deals 150% normal damage when timed correctly
- **Precision Strike**: A focused attack with 120% damage and high accuracy

**Combat Style:** Balanced fighter with emphasis on timing and defensive positioning. Ideal for players who prefer strategic combat over brute force.

### Mongol
The Mongol warrior represents the nomadic excellence of the steppes, combining speed and agility with ranged combat expertise. These warriors are masters of hit-and-run tactics.

**Statistics:**
- Health: 80
- Power: 20
- Defense: 15
- Speed: Very Fast

**Special Abilities:**
- **Swift Strike**: A rapid double-attack dealing 80% damage per hit
- **Eagle Eye**: A ranged bow attack with 130% damage and extended range

**Combat Style:** Fast-paced combat with emphasis on mobility and ranged attacks. Perfect for players who enjoy quick, tactical gameplay.

### Viking
The Viking embodies raw power and berserker fury, representing the fierce warriors of the North. These fighters excel in close combat with devastating area attacks.

**Statistics:**
- Health: 120
- Power: 30
- Defense: 25
- Speed: Slow

**Special Abilities:**
- **Berserker Rage**: An all-out attack dealing 200% damage but causing 10 self-damage
- **Shield Bash**: A stunning attack that deals 70% damage and temporarily disables the enemy

**Combat Style:** Heavy-hitting combat with emphasis on raw damage and defensive capabilities. Ideal for players who prefer direct, powerful confrontations.

## Game Mechanics

### Combat System

The combat system in Warrior Clash 3D is built around strategic turn-based mechanics that emphasize the unique characteristics of each warrior class.

#### Damage Calculation
```
Base Damage = Attacker Power - Random(0, Defender Defense)
Final Damage = Max(1, Base Damage)
```

#### Defense Mechanics
When a player chooses to defend:
- Incoming damage is reduced by 50%
- Defense stat provides additional damage reduction
- Character enters defensive stance animation

#### Special Abilities
Each character class has two unique special abilities that can be activated during combat:
- Abilities have cooldown periods to prevent spam
- Some abilities have risk/reward mechanics (e.g., Berserker Rage)
- Abilities are balanced to maintain competitive gameplay

### Health System
- Each character starts with their class-specific maximum health
- Health is displayed both numerically and through visual health bars
- Health regeneration is not available during combat
- Victory is achieved when opponent's health reaches zero

### Speed Mechanics
Speed affects:
- Turn order in combat
- Animation speed of attacks
- Evasion chances for certain abilities
- Movement speed in the 3D arena

## Controls

### Menu Navigation
- **Mouse Click**: Select character classes and menu options
- **Keyboard**: Arrow keys for navigation (alternative)

### Combat Controls
- **Attack Button**: Execute a basic attack
- **Defend Button**: Enter defensive stance
- **Special Ability Buttons**: Activate character-specific abilities (when available)
- **Return to Menu**: Exit current battle and return to character selection

### 3D Arena Controls
- **Mouse Drag**: Rotate camera around the arena
- **Mouse Wheel**: Zoom in/out of the battle scene
- **Right Click + Drag**: Pan camera view

### Audio Controls
- **Music Toggle**: Play/pause background music
- **Volume**: Controlled through system audio settings

## Technical Specifications

### Technology Stack
- **Frontend Framework**: React 18+ with Vite
- **3D Engine**: Three.js with React Three Fiber
- **3D Components**: React Three Drei for enhanced 3D components
- **Styling**: Tailwind CSS with custom animations
- **Audio**: HTML5 Audio API with MP3 support
- **Build Tool**: Vite for fast development and optimized builds

### Performance Optimizations

#### Mac M1 Specific Optimizations
- **Metal API Support**: Leverages Apple's Metal graphics API through WebGL
- **ARM64 Architecture**: Optimized for Apple Silicon performance
- **Memory Management**: Efficient memory usage for 8GB systems
- **GPU Acceleration**: Utilizes integrated M1 GPU for 3D rendering

#### Rendering Optimizations
- **Level of Detail (LOD)**: Simplified models at distance
- **Frustum Culling**: Only render visible objects
- **Texture Compression**: Optimized texture formats for web delivery
- **Shader Optimization**: Efficient fragment and vertex shaders

#### Memory Management
- **Asset Streaming**: Load assets on-demand
- **Garbage Collection**: Proper cleanup of 3D objects
- **Texture Pooling**: Reuse texture memory when possible
- **Model Instancing**: Efficient rendering of multiple similar objects

### File Structure
```
warrior-clash-3d/
├── public/
│   ├── favicon.ico
│   └── vite.svg
├── src/
│   ├── assets/
│   │   └── middle_east_trap.mp3
│   ├── components/
│   │   └── ui/
│   ├── App.jsx
│   ├── App.css
│   ├── index.css
│   └── main.jsx
├── package.json
├── vite.config.js
└── README.md
```

### Dependencies
```json
{
  "three": "^0.178.0",
  "@react-three/fiber": "^9.2.0",
  "@react-three/drei": "^10.5.0",
  "react": "^19.1.0",
  "react-dom": "^19.1.0",
  "tailwindcss": "^3.4.17"
}
```

## Troubleshooting

### Common Issues

#### Game Won't Start
**Problem**: Error when running `pnpm run dev`
**Solutions**:
1. Ensure Node.js version is 18+ with `node --version`
2. Clear node modules: `rm -rf node_modules && pnpm install`
3. Check for port conflicts: Try `pnpm run dev --port 3000`

#### 3D Models Not Rendering
**Problem**: Black screen or missing 3D elements
**Solutions**:
1. Check browser WebGL support: Visit `webglreport.com`
2. Update browser to latest version
3. Disable browser extensions that might block WebGL
4. Check console for JavaScript errors

#### Audio Not Playing
**Problem**: Background music doesn't play
**Solutions**:
1. Check browser audio permissions
2. Ensure system volume is not muted
3. Try different browser (Safari, Chrome, Firefox)
4. Check if audio file is properly loaded in Network tab

#### Performance Issues
**Problem**: Low frame rate or stuttering
**Solutions**:
1. Close other applications to free memory
2. Reduce browser zoom level to 100%
3. Clear browser cache and cookies
4. Restart browser and try again

### Browser Compatibility
- **Safari**: Recommended for Mac M1 (best performance)
- **Chrome**: Good performance, may use more memory
- **Firefox**: Compatible but may have lower performance
- **Edge**: Compatible on Mac versions

### System Monitoring
Monitor system performance during gameplay:
```bash
# Check CPU usage
top -l 1 | grep "CPU usage"

# Check memory usage
vm_stat | grep "Pages free"

# Check GPU usage (if available)
sudo powermetrics -n 1 -i 1000 | grep "GPU"
```

## Development

### Development Setup
For developers who want to modify or extend the game:

```bash
# Clone the repository
git clone [repository-url]
cd warrior-clash-3d

# Install dependencies
pnpm install

# Start development server
pnpm run dev

# Build for production
pnpm run build

# Preview production build
pnpm run preview
```

### Adding New Characters
To add a new character class:

1. **Define Character Stats** in `src/App.jsx`:
```javascript
const NEW_CHARACTER = {
  name: 'Character Name',
  health: 100,
  power: 25,
  defense: 20,
  speed: 'Fast',
  color: '#HEXCOLOR',
  abilities: ['Ability 1', 'Ability 2']
}
```

2. **Add to CharacterClasses Object**:
```javascript
const CharacterClasses = {
  // ... existing characters
  NEW_CHARACTER: NEW_CHARACTER
}
```

3. **Implement Special Abilities** in the `useSpecialAbility` function

### Modifying Game Mechanics
Key files for game mechanics:
- `src/App.jsx`: Main game logic and combat system
- `src/App.css`: Styling and animations
- `src/assets/`: Game assets (music, textures, models)

### Building for Distribution
```bash
# Create production build
pnpm run build

# The built files will be in the 'dist' directory
# These can be deployed to any web server
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on Mac M1 systems
5. Submit a pull request

---

**Author**: Manus AI  
**Version**: 1.0.0  
**Last Updated**: July 2025  
**License**: MIT License

For support or questions, please refer to the troubleshooting section or contact the development team.

