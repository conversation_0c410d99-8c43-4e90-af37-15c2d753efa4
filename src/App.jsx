import { useState, useRef, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Text, Box, Sphere, Plane } from '@react-three/drei'
import { But<PERSON> } from '@/components/ui/button.jsx'
import './App.css'
import middleEastMusic from './assets/middle_east_trap.mp3'

// Character class definitions
const CharacterClasses = {
  SAMURAI: {
    name: 'Samurai',
    health: 100,
    power: 25,
    defense: 20,
    speed: 'Fast',
    color: '#8B0000',
    abilities: ['Counter Attack', 'Precision Strike']
  },
  MONGOL: {
    name: 'Mongol',
    health: 80,
    power: 20,
    defense: 15,
    speed: 'Very Fast',
    color: '#DAA520',
    abilities: ['Swift Strike', 'Eagle Eye']
  },
  VIKING: {
    name: 'Viking',
    health: 120,
    power: 30,
    defense: 25,
    speed: 'Slow',
    color: '#4682B4',
    abilities: ['Berserker Rage', 'Shield Bash']
  }
}

// Character component
function Character({ position, characterClass, isPlayer = false, health, maxHealth }) {
  const meshRef = useRef()
  const [rotation, setRotation] = useState(0)
  
  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += delta * 0.5
    }
  })

  const healthPercentage = (health / maxHealth) * 100

  return (
    <group position={position}>
      {/* Character body */}
      <Box ref={meshRef} args={[1, 2, 0.5]} position={[0, 1, 0]}>
        <meshStandardMaterial color={characterClass.color} />
      </Box>
      
      {/* Character head */}
      <Sphere args={[0.4]} position={[0, 2.5, 0]}>
        <meshStandardMaterial color={characterClass.color} />
      </Sphere>
      
      {/* Health bar */}
      <group position={[0, 3.5, 0]}>
        <Plane args={[2, 0.2]} position={[0, 0, 0.1]}>
          <meshBasicMaterial color="red" />
        </Plane>
        <Plane args={[2 * (healthPercentage / 100), 0.2]} position={[-(1 - healthPercentage / 100), 0, 0.11]}>
          <meshBasicMaterial color="green" />
        </Plane>
      </group>
      
      {/* Character name */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.3}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {characterClass.name}
      </Text>
      
      {/* Health text */}
      <Text
        position={[0, 3.2, 0]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {health}/{maxHealth}
      </Text>
    </group>
  )
}

// Arena component
function Arena() {
  return (
    <group>
      {/* Ground */}
      <Plane args={[20, 20]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#8B4513" />
      </Plane>
      
      {/* Arena walls */}
      <Box args={[20, 4, 0.5]} position={[0, 2, -10]}>
        <meshStandardMaterial color="#654321" />
      </Box>
      <Box args={[20, 4, 0.5]} position={[0, 2, 10]}>
        <meshStandardMaterial color="#654321" />
      </Box>
      <Box args={[0.5, 4, 20]} position={[-10, 2, 0]}>
        <meshStandardMaterial color="#654321" />
      </Box>
      <Box args={[0.5, 4, 20]} position={[10, 2, 0]}>
        <meshStandardMaterial color="#654321" />
      </Box>
    </group>
  )
}

// Game component
function Game() {
  const [gameState, setGameState] = useState('menu') // 'menu', 'playing', 'gameOver'
  const [selectedClass, setSelectedClass] = useState(null)
  const [playerHealth, setPlayerHealth] = useState(100)
  const [enemyHealth, setEnemyHealth] = useState(100)
  const [enemyClass, setEnemyClass] = useState(CharacterClasses.MONGOL)
  const [isPlayerTurn, setIsPlayerTurn] = useState(true)
  const [gameLog, setGameLog] = useState([])
  const [musicPlaying, setMusicPlaying] = useState(false)
  const audioRef = useRef(null)

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = 0.3
      audioRef.current.loop = true
    }
  }, [])

  const toggleMusic = () => {
    if (audioRef.current) {
      if (musicPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setMusicPlaying(!musicPlaying)
    }
  }

  const startGame = (characterClass) => {
    setSelectedClass(characterClass)
    setPlayerHealth(characterClass.health)
    setEnemyHealth(enemyClass.health)
    setGameState('playing')
    setGameLog([`Battle begins! ${characterClass.name} vs ${enemyClass.name}`])
  }

  const attack = () => {
    if (!isPlayerTurn || gameState !== 'playing') return

    const damage = Math.max(1, selectedClass.power - Math.floor(Math.random() * enemyClass.defense))
    const newEnemyHealth = Math.max(0, enemyHealth - damage)
    setEnemyHealth(newEnemyHealth)
    setGameLog(prev => [...prev, `${selectedClass.name} attacks for ${damage} damage!`])
    
    if (newEnemyHealth <= 0) {
      setGameState('gameOver')
      setGameLog(prev => [...prev, `${selectedClass.name} wins!`])
      return
    }
    
    setIsPlayerTurn(false)
    
    // Enemy turn
    setTimeout(() => {
      const enemyDamage = Math.max(1, enemyClass.power - Math.floor(Math.random() * selectedClass.defense))
      const newPlayerHealth = Math.max(0, playerHealth - enemyDamage)
      setPlayerHealth(newPlayerHealth)
      setGameLog(prev => [...prev, `${enemyClass.name} attacks for ${enemyDamage} damage!`])
      
      if (newPlayerHealth <= 0) {
        setGameState('gameOver')
        setGameLog(prev => [...prev, `${enemyClass.name} wins!`])
      } else {
        setIsPlayerTurn(true)
      }
    }, 1500)
  }

  const defend = () => {
    if (!isPlayerTurn || gameState !== 'playing') return
    
    setGameLog(prev => [...prev, `${selectedClass.name} takes a defensive stance!`])
    setIsPlayerTurn(false)
    
    // Enemy turn with reduced damage
    setTimeout(() => {
      const enemyDamage = Math.max(1, Math.floor((enemyClass.power - selectedClass.defense) * 0.5))
      const newPlayerHealth = Math.max(0, playerHealth - enemyDamage)
      setPlayerHealth(newPlayerHealth)
      setGameLog(prev => [...prev, `${enemyClass.name} attacks but damage is reduced to ${enemyDamage}!`])
      
      if (newPlayerHealth <= 0) {
        setGameState('gameOver')
        setGameLog(prev => [...prev, `${enemyClass.name} wins!`])
      } else {
        setIsPlayerTurn(true)
      }
    }, 1500)
  }

  const resetGame = () => {
    setGameState('menu')
    setSelectedClass(null)
    setPlayerHealth(100)
    setEnemyHealth(100)
    setIsPlayerTurn(true)
    setGameLog([])
  }

  if (gameState === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-amber-900 to-amber-700 flex flex-col items-center justify-center p-8">
        <audio ref={audioRef} src={middleEastMusic} />
        
        <div className="text-center mb-8">
          <h1 className="text-6xl font-bold text-amber-100 mb-4 drop-shadow-lg">
            Warrior Clash 3D
          </h1>
          <p className="text-xl text-amber-200 mb-6">
            Choose your warrior and enter the arena!
          </p>
          
          <Button 
            onClick={toggleMusic}
            className="mb-8 bg-amber-600 hover:bg-amber-500"
          >
            {musicPlaying ? '🔇 Stop Music' : '🎵 Play Music'}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl">
          {Object.values(CharacterClasses).map((characterClass) => (
            <div key={characterClass.name} className="bg-amber-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold text-amber-100 mb-4">{characterClass.name}</h3>
              <div className="text-amber-200 space-y-2 mb-6">
                <p>Health: {characterClass.health}</p>
                <p>Power: {characterClass.power}</p>
                <p>Defense: {characterClass.defense}</p>
                <p>Speed: {characterClass.speed}</p>
                <div>
                  <p className="font-semibold">Abilities:</p>
                  <ul className="list-disc list-inside">
                    {characterClass.abilities.map((ability, index) => (
                      <li key={index}>{ability}</li>
                    ))}
                  </ul>
                </div>
              </div>
              <Button 
                onClick={() => startGame(characterClass)}
                className="w-full bg-amber-600 hover:bg-amber-500"
              >
                Choose {characterClass.name}
              </Button>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      <audio ref={audioRef} src={middleEastMusic} />
      
      {/* Game UI */}
      <div className="absolute top-4 left-4 z-10 bg-black bg-opacity-50 p-4 rounded text-white">
        <h2 className="text-xl font-bold mb-2">Battle Arena</h2>
        <p>Turn: {isPlayerTurn ? 'Your Turn' : 'Enemy Turn'}</p>
        <div className="mt-4 space-y-2">
          <Button 
            onClick={attack} 
            disabled={!isPlayerTurn || gameState !== 'playing'}
            className="mr-2"
          >
            Attack
          </Button>
          <Button 
            onClick={defend} 
            disabled={!isPlayerTurn || gameState !== 'playing'}
            className="mr-2"
          >
            Defend
          </Button>
          <Button onClick={resetGame} variant="outline">
            Return to Menu
          </Button>
        </div>
      </div>

      {/* Game Log */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 p-4 rounded text-white max-w-xs">
        <h3 className="font-bold mb-2">Battle Log</h3>
        <div className="max-h-40 overflow-y-auto">
          {gameLog.map((log, index) => (
            <p key={index} className="text-sm mb-1">{log}</p>
          ))}
        </div>
      </div>

      {/* 3D Scene */}
      <Canvas camera={{ position: [0, 5, 10], fov: 60 }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />
        <directionalLight position={[0, 10, 5]} intensity={1} />
        
        <Arena />
        
        {selectedClass && (
          <>
            <Character 
              position={[-3, 0, 0]} 
              characterClass={selectedClass} 
              isPlayer={true}
              health={playerHealth}
              maxHealth={selectedClass.health}
            />
            <Character 
              position={[3, 0, 0]} 
              characterClass={enemyClass}
              health={enemyHealth}
              maxHealth={enemyClass.health}
            />
          </>
        )}
        
        <OrbitControls enablePan={false} maxPolarAngle={Math.PI / 2} />
      </Canvas>

      {gameState === 'gameOver' && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-20">
          <div className="bg-amber-800 p-8 rounded-lg text-center">
            <h2 className="text-3xl font-bold text-amber-100 mb-4">Game Over!</h2>
            <p className="text-amber-200 mb-6">{gameLog[gameLog.length - 1]}</p>
            <Button onClick={resetGame} className="bg-amber-600 hover:bg-amber-500">
              Play Again
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

function App() {
  return <Game />
}

export default App


// Enhanced combat system with special abilities
const useSpecialAbility = (characterClass, abilityIndex) => {
  const ability = characterClass.abilities[abilityIndex]
  
  switch (ability) {
    case 'Counter Attack':
      return { type: 'counter', damage: characterClass.power * 1.5, description: 'Powerful counter attack!' }
    case 'Precision Strike':
      return { type: 'precision', damage: characterClass.power * 1.2, accuracy: 0.9, description: 'Precise strike that rarely misses!' }
    case 'Swift Strike':
      return { type: 'swift', damage: characterClass.power * 0.8, hits: 2, description: 'Quick double strike!' }
    case 'Eagle Eye':
      return { type: 'ranged', damage: characterClass.power * 1.3, range: true, description: 'Ranged bow attack!' }
    case 'Berserker Rage':
      return { type: 'rage', damage: characterClass.power * 2, selfDamage: 10, description: 'Devastating rage attack!' }
    case 'Shield Bash':
      return { type: 'stun', damage: characterClass.power * 0.7, stun: true, description: 'Stunning shield bash!' }
    default:
      return { type: 'normal', damage: characterClass.power, description: 'Basic attack' }
  }
}

