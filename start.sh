#!/bin/bash

# Warrior Clash 3D - Game Startup Script
# This script will check system requirements and start the game

set -e  # Exit on any error

echo "🎮 Starting Warrior Clash 3D..."
echo "=================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to open browser (cross-platform)
open_browser() {
    local url="$1"
    echo "🌐 Opening browser at: $url"
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$url"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        xdg-open "$url"
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        # Windows
        start "$url"
    else
        echo "⚠️  Please manually open: $url"
    fi
}

# Check Node.js
if ! command_exists node; then
    echo "❌ Node.js is not installed!"
    echo "   Please install from: https://nodejs.org/"
    exit 1
fi
echo "✅ Node.js: $(node --version)"

# Check pnpm
if ! command_exists pnpm; then
    echo "❌ pnpm is not installed!"
    echo "   Installing pnpm..."
    npm install -g pnpm
    if ! command_exists pnpm; then
        echo "❌ Failed to install pnpm. Please install manually:"
        echo "   npm install -g pnpm"
        exit 1
    fi
fi
echo "✅ pnpm: $(pnpm --version)"

# Check if we're in the correct directory
if [[ ! -f "package.json" || ! -f "vite.config.js" ]]; then
    echo "❌ Not in the correct game directory!"
    echo "   Make sure you're in the warrior-clash-3d directory"
    exit 1
fi
echo "✅ Game directory: Detected"

# Install dependencies if node_modules doesn't exist
if [[ ! -d "node_modules" ]]; then
    echo "📦 Installing dependencies..."
    pnpm install
    if [[ $? -ne 0 ]]; then
        echo "❌ Failed to install dependencies!"
        exit 1
    fi
else
    echo "✅ Dependencies: Already installed"
fi

# Check if port 5173 is already in use
if command_exists lsof && lsof -i :5173 >/dev/null 2>&1; then
    echo "⚠️  Port 5173 is already in use!"
    echo "   The game might already be running."
    echo "   Check: http://localhost:5173/"
    
    read -p "   Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "   Cancelled."
        exit 0
    fi
fi

echo ""
echo "🚀 Starting the game server..."
echo "   Game will be available at: http://localhost:5173/"
echo "   Press Ctrl+C to stop the game"
echo ""

# Start the development server
pnpm run dev &
DEV_PID=$!

# Wait a moment for the server to start
sleep 3

# Open browser automatically
open_browser "http://localhost:5173/"

# Wait for the development server
wait $DEV_PID
